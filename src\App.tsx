import React from 'react';
import { KeyboardIcon } from 'lucide-react';
import { useTypingStore } from './store/typingStore';
import TestModeSelector from './components/TestModeSelector';
import Stats from './components/Stats';
import TypingTest from './components/TypingTest';

function App() {
  const isTestActive = useTypingStore(state => state.testState.isTestActive);

  return (
    <div className="min-h-screen bg-black">
      <header className="bg-black shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <KeyboardIcon className="h-6 w-6 text-yellow-400" />
            <h1 className="text-xl font-bold text-yellow-400">TypeMaster Pro</h1>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {!isTestActive && (
          <div className="mb-8">
            <TestModeSelector />
          </div>
        )}

        <div className="grid gap-8 md:grid-cols-[2fr,1fr]">
          <TypingTest />
          <Stats />
        </div>
      </main>
    </div>
  );
}

export default App;