import React from 'react';
import { useTypingStore } from '../store/typingStore';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

function Stats() {
  const testState = useTypingStore(state => state.testState);
  const testResults = useTypingStore(state => state.testResults);
  const words = useTypingStore(state => state.words);

  const calculateWPM = () => {
    if (testState.totalChars === 0) return 0;
    const timeElapsed = 60 - testState.timeLeft;
    if (timeElapsed === 0) return 0;
    // Standard WPM calculation: (characters typed / 5) / (time in minutes)
    // Using total characters typed (including mistakes) for more accurate real-time WPM
    return Math.round((testState.totalChars / 5) / (timeElapsed / 60));
  };

  const calculateAccuracy = () => {
    if (testState.totalChars === 0) return 100;
    return Math.round((testState.correctChars / testState.totalChars) * 100);
  };

  const recentResults = testResults.slice(-10).map((result, index) => ({
    test: index + 1,
    wpm: result.wpm,
    accuracy: result.accuracy,
  }));

  // Get current weak words (words with low typing speed)
  const weakWords = words.filter(word =>
    word.attempts > 0 && word.averageSpeed < 50
  ).sort((a, b) => a.averageSpeed - b.averageSpeed).slice(0, 10);

  return (
    <div className="space-y-8">
      {/* Current Test Stats */}
      <div className="bg-gray-900 rounded-lg shadow-sm p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-yellow-400 mb-4">Current Test</h3>
        <div className="grid grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400">{calculateWPM()}</div>
            <div className="text-sm text-gray-400">WPM</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400">{calculateAccuracy()}%</div>
            <div className="text-sm text-gray-400">Accuracy</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-400">{testState.mistakes}</div>
            <div className="text-sm text-gray-400">Mistakes</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">{testState.timeLeft}s</div>
            <div className="text-sm text-gray-400">Time Left</div>
          </div>
        </div>
      </div>

      {/* Performance Chart - Full Width */}
      {recentResults.length > 0 && (
        <div className="bg-gray-900 rounded-lg shadow-sm p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-yellow-400 mb-4">Recent Performance</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={recentResults}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="test" tick={{ fill: '#9CA3AF' }} />
                <YAxis tick={{ fill: '#9CA3AF' }} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1F2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F3F4F6'
                  }}
                />
                <Bar dataKey="wpm" fill="#60A5FA" name="WPM" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {/* Bottom Grid - Weak Words and Test History Side by Side */}
      <div className="grid gap-8 md:grid-cols-2">
        {/* Weak Words Section */}
        {weakWords.length > 0 && (
          <div className="bg-gray-900 rounded-lg shadow-sm p-6 border border-gray-700">
            <h3 className="text-lg font-semibold text-yellow-400 mb-4">Weak Words</h3>
            <div className="text-sm text-gray-400 mb-3">
              Words you need to practice (typing speed &lt; 50 WPM)
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {weakWords.map((word, index) => (
                <div key={word.id} className="flex justify-between items-center py-2 px-3 bg-gray-800 rounded">
                  <span className="text-red-400 font-medium">{word.text}</span>
                  <div className="text-xs text-gray-400">
                    {Math.round(word.averageSpeed)} WPM
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Test History */}
        {testResults.length > 0 && (
          <div className="bg-gray-900 rounded-lg shadow-sm p-6 border border-gray-700">
            <h3 className="text-lg font-semibold text-yellow-400 mb-4">Test History</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {testResults.slice(-5).reverse().map((result, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-700 last:border-b-0">
                  <div className="text-sm text-gray-400">
                    {new Date(result.date).toLocaleDateString()}
                  </div>
                  <div className="flex space-x-4 text-sm">
                    <span className="text-blue-400 font-medium">{result.wpm} WPM</span>
                    <span className="text-green-400 font-medium">{result.accuracy}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Stats;