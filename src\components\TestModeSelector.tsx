import React from 'react';
import { useTypingStore } from '../store/typingStore';

function TestModeSelector() {
  const testMode = useTypingStore(state => state.testState.testMode);
  const setTestMode = useTypingStore(state => state.setTestMode);

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Mode</h2>
      <div className="flex space-x-4">
        <button
          onClick={() => setTestMode('practice')}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            testMode === 'practice'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          Practice Mode
        </button>
        <button
          onClick={() => setTestMode('weak-words')}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            testMode === 'weak-words'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          Weak Words
        </button>
      </div>
      <div className="mt-3 text-sm text-gray-600">
        {testMode === 'practice'
          ? 'Practice with common words to improve your typing speed.'
          : 'Focus on words you struggle with most.'
        }
      </div>
    </div>
  );
}

export default TestModeSelector;