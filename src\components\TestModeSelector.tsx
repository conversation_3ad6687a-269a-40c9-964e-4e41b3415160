import React from 'react';
import { useTypingStore } from '../store/typingStore';

function TestModeSelector() {
  const testMode = useTypingStore(state => state.testState.testMode);
  const includeCapitals = useTypingStore(state => state.testState.includeCapitals);
  const includePunctuation = useTypingStore(state => state.testState.includePunctuation);
  const includeNumbers = useTypingStore(state => state.testState.includeNumbers);
  const setTestMode = useTypingStore(state => state.setTestMode);
  const toggleCapitals = useTypingStore(state => state.toggleCapitals);
  const togglePunctuation = useTypingStore(state => state.togglePunctuation);
  const toggleNumbers = useTypingStore(state => state.toggleNumbers);

  return (
    <div className="bg-gray-900 rounded-lg shadow-sm p-6 border border-gray-700">
      <h2 className="text-lg font-semibold text-yellow-400 mb-4">Test Mode</h2>

      {/* Test Mode Selection */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={() => setTestMode('practice')}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            testMode === 'practice'
              ? 'bg-yellow-400 text-black'
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
        >
          Practice Mode
        </button>
        <button
          onClick={() => setTestMode('weak-words')}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            testMode === 'weak-words'
              ? 'bg-yellow-400 text-black'
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
        >
          Weak Words
        </button>
      </div>

      {/* Enhancement Toggles - Only show for Practice Mode */}
      {testMode === 'practice' && (
        <div className="border-t border-gray-700 pt-4">
          <h3 className="text-md font-medium text-yellow-400 mb-3">Practice Options</h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={toggleCapitals}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                includeCapitals
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Capital Letters
            </button>
            <button
              onClick={togglePunctuation}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                includePunctuation
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Punctuation
            </button>
            <button
              onClick={toggleNumbers}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                includeNumbers
                  ? 'bg-purple-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Numbers
            </button>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Enhancements will be applied to up to 20% of characters
          </div>
        </div>
      )}

      {/* Mode Description */}
      <div className="mt-4 text-sm text-gray-400">
        {testMode === 'practice' && 'Practice with common words to improve your typing speed.'}
        {testMode === 'weak-words' && 'Focus on words you struggle with most.'}
      </div>
    </div>
  );
}

export default TestModeSelector;