import React from 'react';
import { useEffect } from 'react';
import { useTypingStore } from '../store/typingStore';

function Timer() {
  const timeLeft = useTypingStore(state => state.testState.timeLeft);
  const isTestActive = useTypingStore(state => state.testState.isTestActive);
  const updateTimer = useTypingStore(state => state.updateTimer);
  const endTest = useTypingStore(state => state.endTest);

  useEffect(() => {
    let intervalId: number;

    if (isTestActive && timeLeft > 0) {
      intervalId = window.setInterval(() => {
        updateTimer();
      }, 1000);
    } else if (timeLeft === 0 && isTestActive) {
      endTest();
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isTestActive, timeLeft, updateTimer, endTest]);

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  return (
    <div className="text-2xl font-mono font-bold text-gray-700">
      {minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')}
    </div>
  );
}

export default Timer;