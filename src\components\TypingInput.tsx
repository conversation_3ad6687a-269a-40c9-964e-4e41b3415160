import React, { forwardRef, useEffect } from 'react';
import { useTypingStore } from '../store/typingStore';

interface TypingInputProps {
  className?: string;
}

const TypingInput = forwardRef<HTMLInputElement, TypingInputProps>(
  ({ className = '' }, ref) => {
    const currentInput = useTypingStore(state => state.testState.currentInput);
    const isTestActive = useTypingStore(state => state.testState.isTestActive);
    const isPaused = useTypingStore(state => state.testState.isPaused);
    const updateInput = useTypingStore(state => state.updateInput);

    useEffect(() => {
      if (isTestActive && ref && 'current' in ref && ref.current) {
        ref.current.focus();
      }
    }, [isTestActive, ref]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (isTestActive && !isPaused) {
        updateInput(e.target.value);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Tab') {
        e.preventDefault();
      }
    };

    return (
      <div className="mt-6">
        <input
          ref={ref}
          type="text"
          value={currentInput}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={!isTestActive || isPaused}
          className={`w-full px-4 py-3 text-xl font-lexend bg-transparent border-2 border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 disabled:bg-gray-800 disabled:cursor-not-allowed tracking-wide text-yellow-400 placeholder-gray-500 ${className}`}
          placeholder={isTestActive ? (isPaused ? "Test paused..." : "Start typing...") : "Press Space to start"}
          autoComplete="off"
          spellCheck={false}
        />
      </div>
    );
  }
);

TypingInput.displayName = 'TypingInput';

export default TypingInput;