import React, { forwardRef, useEffect } from 'react';
import { useTypingStore } from '../store/typingStore';

interface TypingInputProps {
  className?: string;
}

const TypingInput = forwardRef<HTMLInputElement, TypingInputProps>(
  ({ className = '' }, ref) => {
    const currentInput = useTypingStore(state => state.testState.currentInput);
    const isTestActive = useTypingStore(state => state.testState.isTestActive);
    const updateInput = useTypingStore(state => state.updateInput);

    useEffect(() => {
      if (isTestActive && ref && 'current' in ref && ref.current) {
        ref.current.focus();
      }
    }, [isTestActive, ref]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (isTestActive) {
        updateInput(e.target.value);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Tab') {
        e.preventDefault();
      }
    };

    return (
      <div className="mt-6">
        <input
          ref={ref}
          type="text"
          value={currentInput}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={!isTestActive}
          className={`w-full px-4 py-3 text-lg font-lexend border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed tracking-wide ${className}`}
          placeholder={isTestActive ? "Start typing..." : "Press Space to start"}
          autoComplete="off"
          spellCheck={false}
        />
      </div>
    );
  }
);

TypingInput.displayName = 'TypingInput';

export default TypingInput;