import React, { useEffect, useRef } from 'react';
import { useTypingStore } from '../store/typingStore';
import Timer from './Timer';
import WordContainer from './WordContainer';
import TypingInput from './TypingInput';

function TypingTest() {
  const inputRef = useRef<HTMLInputElement>(null);
  const isTestActive = useTypingStore(state => state.testState.isTestActive);
  const initTest = useTypingStore(state => state.initTest);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && !isTestActive) {
        e.preventDefault();
        initTest();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isTestActive, initTest]);

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Typing Test</h2>
        <Timer />
      </div>

      <WordContainer />
      <TypingInput ref={inputRef} />

      {!isTestActive && (
        <div className="mt-4 text-center text-gray-600">
          Press <kbd className="px-2 py-1 bg-gray-100 rounded">Space</kbd> to start the test
        </div>
      )}
    </div>
  );
}

export default TypingTest;