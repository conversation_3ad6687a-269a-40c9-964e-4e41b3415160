import React, { useEffect, useRef, useState } from 'react';
import { useTypingStore } from '../store/typingStore';
import Timer from './Timer';
import WordContainer from './WordContainer';
import TypingInput from './TypingInput';
import { RotateCcw, Play, Pause } from 'lucide-react';

function TypingTest() {
  const inputRef = useRef<HTMLInputElement>(null);
  const [lastActivityTime, setLastActivityTime] = useState(Date.now());
  const [isWindowFocused, setIsWindowFocused] = useState(true);

  const {
    isTestActive,
    isPaused,
    initTest,
    restartTest,
    pauseTest,
    resumeTest
  } = useTypingStore(state => ({
    isTestActive: state.testState.isTestActive,
    isPaused: state.testState.isPaused,
    initTest: state.initTest,
    restartTest: state.restartTest,
    pauseTest: state.pauseTest,
    resumeTest: state.resumeTest,
  }));

  // Auto-pause functionality
  useEffect(() => {
    const handleActivity = () => {
      setLastActivityTime(Date.now());
      if (isPaused && isTestActive) {
        resumeTest();
      }
    };

    const handleFocus = () => {
      setIsWindowFocused(true);
      if (isPaused && isTestActive) {
        resumeTest();
      }
    };

    const handleBlur = () => {
      setIsWindowFocused(false);
      if (isTestActive && !isPaused) {
        pauseTest();
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      handleActivity();
      if (e.code === 'Space' && !isTestActive) {
        e.preventDefault();
        initTest();
      }
    };

    // Check for inactivity every second
    const inactivityTimer = setInterval(() => {
      if (isTestActive && !isPaused && isWindowFocused) {
        const timeSinceActivity = Date.now() - lastActivityTime;
        if (timeSinceActivity > 3000) { // 3 seconds of inactivity
          pauseTest();
        }
      }
    }, 1000);

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('mousemove', handleActivity);
    window.addEventListener('click', handleActivity);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('mousemove', handleActivity);
      window.removeEventListener('click', handleActivity);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      clearInterval(inactivityTimer);
    };
  }, [isTestActive, isPaused, isWindowFocused, lastActivityTime, initTest, pauseTest, resumeTest]);

  return (
    <div className="min-h-screen bg-black text-yellow-400 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header with controls */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <button
                onClick={restartTest}
                className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                title="Restart Test"
              >
                <RotateCcw size={16} />
                <span className="text-sm">Restart</span>
              </button>

              {isTestActive && (
                <button
                  onClick={isPaused ? resumeTest : pauseTest}
                  className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                  title={isPaused ? "Resume Test" : "Pause Test"}
                >
                  {isPaused ? <Play size={16} /> : <Pause size={16} />}
                  <span className="text-sm">{isPaused ? 'Resume' : 'Pause'}</span>
                </button>
              )}
            </div>
          </div>
          <Timer />
        </div>

        {/* Pause overlay */}
        {isPaused && isTestActive && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 p-8 rounded-lg text-center">
              <Pause size={48} className="mx-auto mb-4 text-yellow-400" />
              <h2 className="text-xl font-bold text-yellow-400 mb-2">Test Paused</h2>
              <p className="text-gray-300 mb-4">Click anywhere or press any key to resume</p>
              <button
                onClick={resumeTest}
                className="px-4 py-2 bg-yellow-400 text-gray-900 rounded-lg hover:bg-yellow-300 transition-colors"
              >
                Resume Test
              </button>
            </div>
          </div>
        )}

        {/* Main typing area */}
        <div className="bg-black rounded-lg p-8 shadow-xl">
          <WordContainer />
          <TypingInput ref={inputRef} />

          {!isTestActive && (
            <div className="mt-6 text-center text-gray-400">
              Press <kbd className="px-2 py-1 bg-gray-700 text-yellow-400 rounded">Space</kbd> to start the test
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default TypingTest;