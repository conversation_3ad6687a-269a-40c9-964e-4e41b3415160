import React from 'react';
import { useTypingStore } from '../store/typingStore';

function WordContainer() {
  const currentWords = useTypingStore(state => state.currentWords);
  const currentIndex = useTypingStore(state => state.testState.currentIndex);
  const currentInput = useTypingStore(state => state.testState.currentInput);

  const getWordClass = (wordIndex: number, word: string) => {
    if (wordIndex < currentIndex) {
      return 'text-green-600'; // Completed words
    } else if (wordIndex === currentIndex) {
      // Current word - check if input matches so far or if there are mistakes
      if (currentInput.length === 0) {
        return 'text-blue-600 bg-blue-50'; // No input yet, show as current
      }

      // Check if input is correct so far
      const isCorrectSoFar = currentInput.length <= word.length &&
                            word.substring(0, currentInput.length) === currentInput;

      // Check if input is longer than the word (extra characters)
      const hasExtraChars = currentInput.length > word.length;

      if (hasExtraChars || !isCorrectSoFar) {
        return 'text-red-600 bg-red-50'; // Show red for mistakes or extra characters
      } else {
        return 'text-blue-600 bg-blue-50'; // Show blue for correct input so far
      }
    } else {
      return 'text-gray-400'; // Future words
    }
  };

  const getCharacterClass = (wordIndex: number, charIndex: number, char: string) => {
    if (wordIndex !== currentIndex) return '';

    if (charIndex < currentInput.length) {
      const inputChar = currentInput[charIndex];
      return inputChar === char ? 'bg-green-200' : 'bg-red-200';
    } else if (charIndex === currentInput.length) {
      return 'bg-gray-300'; // Current cursor position
    }

    return '';
  };

  if (currentWords.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No words loaded. Press Space to start a test.
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg p-6 min-h-[200px] font-mono text-lg leading-relaxed">
      <div className="flex flex-wrap gap-x-3 gap-y-2">
        {currentWords.slice(0, 50).map((word, wordIndex) => (
          <span
            key={`${word.id}-${wordIndex}`}
            className={`px-2 py-1 rounded transition-colors duration-200 ${getWordClass(wordIndex, word.text)}`}
          >
            {word.text.split('').map((char, charIndex) => (
              <span
                key={charIndex}
                className={getCharacterClass(wordIndex, charIndex, char)}
              >
                {char}
              </span>
            ))}
            {/* Show extra characters if user typed more than the word length */}
            {wordIndex === currentIndex && currentInput.length > word.text.length && (
              <span className="bg-red-200 text-red-800">
                {currentInput.substring(word.text.length)}
              </span>
            )}
          </span>
        ))}
      </div>

      {currentIndex < currentWords.length && (
        <div className="mt-4 text-sm text-gray-600">
          Word {currentIndex + 1} of {currentWords.length}
        </div>
      )}
    </div>
  );
}

export default WordContainer;