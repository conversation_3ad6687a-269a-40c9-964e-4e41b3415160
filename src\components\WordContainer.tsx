import React, { useEffect, useRef } from 'react';
import { useTypingStore } from '../store/typingStore';

function WordContainer() {
  const currentWords = useTypingStore(state => state.currentWords);
  const currentIndex = useTypingStore(state => state.testState.currentIndex);
  const currentInput = useTypingStore(state => state.testState.currentInput);
  const containerRef = useRef<HTMLDivElement>(null);

  const getWordClass = (wordIndex: number, word: string) => {
    if (wordIndex < currentIndex) {
      return 'text-gray-500'; // Completed words (grayed out)
    } else if (wordIndex === currentIndex) {
      // Current word - check if input matches so far or if there are mistakes
      if (currentInput.length === 0) {
        return 'text-yellow-400'; // No input yet, show as current
      }

      // Check if input is correct so far
      const isCorrectSoFar = currentInput.length <= word.length &&
                            word.substring(0, currentInput.length) === currentInput;

      // Check if input is longer than the word (extra characters)
      const hasExtraChars = currentInput.length > word.length;

      if (hasExtraChars || !isCorrectSoFar) {
        return 'text-red-400'; // Show red for mistakes or extra characters
      } else {
        return 'text-yellow-400'; // Show yellow for correct input so far
      }
    } else {
      return 'text-gray-400'; // Future words
    }
  };

  const getCharacterClass = (wordIndex: number, charIndex: number, char: string) => {
    if (wordIndex !== currentIndex) return '';

    if (charIndex < currentInput.length) {
      const inputChar = currentInput[charIndex];
      return inputChar === char ? 'bg-gray-600 text-gray-400' : 'bg-red-600 text-red-200';
    } else if (charIndex === currentInput.length) {
      return 'bg-yellow-400 text-gray-900 animate-pulse'; // Current cursor position
    }

    return '';
  };

  // Calculate words per line (approximately) - reduced for larger text
  const wordsPerLine = 12; // Increased slightly with reduced gaps
  const totalLines = Math.ceil(currentWords.length / wordsPerLine);
  const currentLine = Math.floor(currentIndex / wordsPerLine);

  // Show 2 lines at a time, with the current word on the first line
  const startLine = Math.max(0, currentLine);
  const endLine = Math.min(totalLines, startLine + 2);

  const startWordIndex = startLine * wordsPerLine;
  const endWordIndex = Math.min(currentWords.length, endLine * wordsPerLine);
  const visibleWords = currentWords.slice(startWordIndex, endWordIndex);

  // Auto-scroll effect when moving to next line
  useEffect(() => {
    if (containerRef.current) {
      const currentWordLine = Math.floor(currentIndex / wordsPerLine);
      const displayStartLine = Math.floor(startWordIndex / wordsPerLine);

      // If current word is not in the first line of display, scroll
      if (currentWordLine > displayStartLine) {
        containerRef.current.style.transform = `translateY(-${(currentWordLine - displayStartLine) * 72}px)`;
        containerRef.current.style.transition = 'transform 0.4s ease-in-out';
      }
    }
  }, [currentIndex, startWordIndex, wordsPerLine]);

  if (currentWords.length === 0) {
    return (
      <div className="text-center py-8 text-gray-400 font-lexend">
        No words loaded. Press Space to start a test.
      </div>
    );
  }

  // Group words into lines
  const lines: Array<Array<{ word: any; wordIndex: number }>> = [];
  for (let i = 0; i < visibleWords.length; i += wordsPerLine) {
    const lineWords = visibleWords.slice(i, i + wordsPerLine).map((word, idx) => ({
      word,
      wordIndex: startWordIndex + i + idx
    }));
    lines.push(lineWords);
  }

  return (
    <div className="rounded-lg p-6 min-h-[160px] font-lexend text-2xl leading-relaxed overflow-hidden">
      <div ref={containerRef} className="transition-transform duration-300 ease-in-out">
        {lines.map((line, lineIndex) => (
          <div key={lineIndex} className="flex flex-nowrap gap-x-3 mb-6 h-12 items-center overflow-hidden">
            {line.map(({ word, wordIndex }) => (
              <span
                key={`${word.id}-${wordIndex}`}
                className={`px-2 py-1 rounded transition-colors duration-200 whitespace-nowrap ${getWordClass(wordIndex, word.text)}`}
              >
                {word.text.split('').map((char, charIndex) => (
                  <span
                    key={charIndex}
                    className={getCharacterClass(wordIndex, charIndex, char)}
                  >
                    {char}
                  </span>
                ))}
                {/* Show extra characters if user typed more than the word length */}
                {wordIndex === currentIndex && currentInput.length > word.text.length && (
                  <span className="bg-red-600 text-red-200">
                    {currentInput.substring(word.text.length)}
                  </span>
                )}
              </span>
            ))}
          </div>
        ))}
      </div>

      {currentIndex < currentWords.length && (
        <div className="mt-6 text-base text-gray-500 font-lexend">
          Word {currentIndex + 1} of {currentWords.length} • Line {Math.floor(currentIndex / wordsPerLine) + 1} of {totalLines}
        </div>
      )}
    </div>
  );
}

export default WordContainer;