export const commonWords = [
  'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'I',
  'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
  'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she',
  'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their',
  'what', 'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go',
  'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know',
  'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them',
  'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over',
  'think', 'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first',
  'well', 'way', 'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day',
  'most', 'us', 'is', 'water', 'long', 'very', 'after', 'here', 'should', 'around',
  'through', 'where', 'much', 'before', 'move', 'right', 'boy', 'old', 'too', 'same',
  'tell', 'does', 'set', 'three', 'high', 'why', 'air', 'well', 'large', 'must',
  'big', 'small', 'another', 'home', 'world', 'still', 'between', 'never', 'last', 'might',
  'great', 'little', 'own', 'under', 'read', 'every', 'don', 'does', 'got', 'united',
  'left', 'number', 'course', 'war', 'until', 'always', 'away', 'something', 'fact', 'though',
  'water', 'less', 'public', 'put', 'think', 'almost', 'hand', 'enough', 'far', 'took',
  'head', 'yet', 'government', 'system', 'better', 'set', 'told', 'nothing', 'night', 'end',
  'why', 'called', 'didn', 'eyes', 'find', 'going', 'look', 'asked', 'later', 'knew',
  'point', 'next', 'week', 'case', 'young', 'asked', 'worked', 'government', 'number', 'group',
  'problem', 'turned', 'run', 'book', 'eye', 'never', 'last', 'let', 'thought', 'city',
  'tree', 'cross', 'since', 'hard', 'start', 'might', 'story', 'saw', 'far', 'sea',
  'draw', 'left', 'late', 'run', 'don', 'while', 'press', 'close', 'night', 'real'
].map((word, id) => ({
  id,
  text: word,
  attempts: 0,
  correctAttempts: 0,
  averageSpeed: 0,
  totalTime: 0,
}));