import { create } from 'zustand';
import { commonWords } from '../data/wordList';
import { TestState, Word, TestResult } from '../types';

interface TypingStore {
  words: Word[];
  currentWords: Word[];
  testResults: TestResult[];
  testState: TestState;
  initTest: () => void;
  updateInput: (input: string) => void;
  setTestMode: (mode: 'practice' | 'weak-words') => void;
  updateTimer: () => void;
  endTest: () => void;
}

export const useTypingStore = create<TypingStore>((set, get) => ({
  words: commonWords,
  currentWords: [],
  testResults: [],
  testState: {
    currentIndex: 0,
    currentInput: '',
    isTestActive: false,
    testMode: 'practice',
    timeLeft: 60,
    mistakes: 0,
    correctChars: 0,
    totalChars: 0,
  },
  initTest: () => {
    const { testState } = get();
    const words = testState.testMode === 'practice' 
      ? [...commonWords].sort(() => Math.random() - 0.5).slice(0, 50)
      : get().words.filter(w => w.attempts > 0 && (w.correctAttempts / w.attempts) < 0.8).slice(0, 50);
    
    set({
      currentWords: words,
      testState: {
        ...testState,
        currentIndex: 0,
        currentInput: '',
        isTestActive: true,
        timeLeft: 60,
        mistakes: 0,
        correctChars: 0,
        totalChars: 0,
      }
    });
  },
  updateInput: (input: string) => {
    const { testState, currentWords } = get();
    const currentWord = currentWords[testState.currentIndex];

    if (!currentWord) return;

    // Check if word is completed (only when space is pressed)
    if (input.endsWith(' ')) {
      const wordInput = input.trim(); // Remove the space
      const isCorrect = wordInput === currentWord.text;

      // Update word statistics
      const updatedWords = get().words.map(word =>
        word.id === currentWord.id
          ? {
              ...word,
              attempts: word.attempts + 1,
              correctAttempts: word.correctAttempts + (isCorrect ? 1 : 0)
            }
          : word
      );

      // Update test state
      set(state => ({
        words: updatedWords,
        testState: {
          ...state.testState,
          currentIndex: state.testState.currentIndex + 1,
          currentInput: '',
          mistakes: state.testState.mistakes + (isCorrect ? 0 : 1),
          correctChars: state.testState.correctChars + (isCorrect ? currentWord.text.length : 0),
          totalChars: state.testState.totalChars + currentWord.text.length,
        }
      }));
    } else {
      // Update current input (don't advance word until space is pressed)
      set(state => ({
        testState: {
          ...state.testState,
          currentInput: input,
        }
      }));
    }
  },
  setTestMode: (mode) => {
    set(state => ({
      testState: { ...state.testState, testMode: mode }
    }));
  },
  updateTimer: () => {
    set(state => ({
      testState: {
        ...state.testState,
        timeLeft: Math.max(0, state.testState.timeLeft - 1),
      }
    }));
  },
  endTest: () => {
    const { testState } = get();

    // Calculate final stats
    const wpm = testState.totalChars > 0
      ? Math.round((testState.correctChars / 5) / ((60 - testState.timeLeft) / 60))
      : 0;
    const accuracy = testState.totalChars > 0
      ? Math.round((testState.correctChars / testState.totalChars) * 100)
      : 100;

    // Find weak words (words with low accuracy)
    const weakWords = get().words.filter(word =>
      word.attempts > 0 && (word.correctAttempts / word.attempts) < 0.8
    );

    // Create test result
    const testResult: TestResult = {
      date: Date.now(),
      wpm,
      accuracy,
      weakWords: weakWords.slice(0, 10), // Top 10 weak words
    };

    // Update state
    set(state => ({
      testResults: [...state.testResults, testResult],
      testState: {
        ...state.testState,
        isTestActive: false,
      }
    }));
  }
}));