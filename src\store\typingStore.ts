import { create } from 'zustand';
import { commonWords } from '../data/wordList';
import { TestState, Word, TestResult } from '../types';

interface TypingStore {
  words: Word[];
  currentWords: Word[];
  testResults: TestResult[];
  testState: TestState;
  initTest: () => void;
  updateInput: (input: string) => void;
  setTestMode: (mode: 'practice' | 'weak-words' | 'story') => void;
  updateTimer: () => void;
  endTest: () => void;
  restartTest: () => void;
  pauseTest: () => void;
  resumeTest: () => void;
}

// Pre-written stories for story mode
const stories = [
  `It was 7:45 a.m. when <PERSON>'s alarm buzzed—again. He hit snooze (for the third time), rolled over, and stared at the ceiling.
"Today has to be better," he said to himself.

He jumped up, brushed his teeth, washed his face, and got dressed. Shirt? Check. Shoes? Check. Bag? Ugh—where was it?

Downstai<PERSON>, his little sister shouted, "You're late!"
<PERSON> grabbed a banana, a slice of toast, and ran out the door.

The air was cold—42°F—with wind blowing at 12 mph. He zipped up his jacket and crossed the street at 8:01 a.m. sharp. Just then, a black dog barked loudly from behind a white fence.
"Whoa!" <PERSON> stepped back. His heart raced.

When he reached the bus stop, only three people were waiting: a man with a red hat, a woman reading a book, and a boy with two backpacks. The bus arrived at 8:09 a.m.—on time for once.

School started at 8:30, but today was different. Everyone was buzzing about "The Big Announcement." No one knew what it meant.

At exactly 9:00 a.m., the principal walked in and said:
"Hello students, staff, and friends. As of today, our school will be going 100% digital. No more paper, no more pens—just tablets, screens, and clouds."

The room was silent. Then... chaos!
"No more notebooks?"
"What about my drawing pad?"
"Will we get to keep the tablets?"

John didn't know whether to be happy or worried. On one hand, typing was easier. On the other, he liked drawing in the margins, using sticky notes, and scribbling weird symbols like &*@# just for fun.

At lunch, his friend Sam said, "Guess we'll need to type fast now. Bet I can type 90 words per minute."

John smiled. "Wanna race?"
"Done!" shouted Sam.
John laughed, "You win this time. But I'll be ready tomorrow."`,

  `Ella found an old box in the attic. Inside: a torn paper, a small key, and a note.

"Look under the red stone by the oak tree—3 paces north, 5 steps east."

"What does this mean?" she whispered.

She grabbed her coat, ran outside, and found the stone. It was larger than expected and shaped like a heart. Digging beneath it with a stick, she found a metal box marked with a strange symbol: @#&.

Click—the key fit!

Inside were photos, coins dated 1945, and a folded map. In the corner, it read:

"To the one who dares: Begin at 6:00 a.m. Don't be late."

She looked at her phone.
5:47 a.m.
"I've got 13 minutes."

She smiled and ran down the path, heart pounding.`,

  `Mark opened his science book and noticed a tiny note stuck between pages 74 and 75.

"If you can read this, meet me at room 304 at 2:45 p.m. —J"

He checked the time: 2:18 p.m.

He whispered, "Is this a joke?" But curiosity won.

Room 304 was quiet. On the table sat a laptop and a second note:

"Solve this: 8 + 4 × 3 = ?
Hint: Use the order of operations ;)"

He typed: 20.

Correct! the screen blinked.

Next challenge:

"Decode: T=h, F=o, S=r, Z=e. What is: ZFTT?"

"Hmm... Z is E, F is O, T is H, S is R... E-O-H-R?"

He laughed. "Hero! That's me!"

Just then, the screen said:

"Welcome to the team."`,

  `At 6:59 a.m., Mia got an email with the subject line:
"Don't miss this—only 1 hour!"

She clicked it. Inside was a single line of text:

"Go to https://bit.ly/Find-YouR-Way"

She paused. It looked shady, but curiosity won.

Click.

The page opened:
"To begin, type the secret passcode: 9F!z&2@L"

She typed slowly:
9... F... exclamation mark... z... and... 2... at... L.

Access granted.

The screen showed a spinning globe. A message appeared:

"Your mission begins now. Use this sequence:
Left, right, down, up, A, B, 1, 2, %, $, ?"

Mia smiled.
"A secret game? At this hour?"

She leaned forward, cracked her knuckles, and typed fast.

Today was going to be fun.`
];

const generateStory = (): Word[] => {
  // Randomly select one of the pre-written stories
  const selectedStory = stories[Math.floor(Math.random() * stories.length)];

  // Split into words and create Word objects, preserving all formatting
  const words = selectedStory.split(/\s+/).map((word, index) => ({
    id: index,
    text: word,
    attempts: 0,
    correctAttempts: 0,
    averageSpeed: 0,
    totalTime: 0,
  }));

  return words;
};

export const useTypingStore = create<TypingStore>((set, get) => ({
  words: commonWords,
  currentWords: [],
  testResults: [],
  testState: {
    currentIndex: 0,
    currentInput: '',
    isTestActive: false,
    isPaused: false,
    testMode: 'practice',
    timeLeft: 60,
    mistakes: 0,
    correctChars: 0,
    totalChars: 0,
  },
  initTest: () => {
    const { testState } = get();
    const targetWordCount = 200;

    let words: Word[] = [];

    if (testState.testMode === 'practice') {
      // Practice mode: use random words from the common words list
      const shuffledWords = [...commonWords].sort(() => Math.random() - 0.5);

      // Repeat the word list if we need more than available
      while (words.length < targetWordCount) {
        const remainingNeeded = targetWordCount - words.length;
        const wordsToAdd = shuffledWords.slice(0, Math.min(remainingNeeded, shuffledWords.length));
        words.push(...wordsToAdd);
      }
    } else if (testState.testMode === 'story') {
      // Story mode: generate a coherent story with proper punctuation and capitalization
      words = generateStory();
    } else {
      // Weak words mode: focus on words with typing speed less than 50 WPM
      const weakWords = get().words.filter(w => w.attempts > 0 && w.averageSpeed < 50);

      if (weakWords.length === 0) {
        // No weak words yet, use practice mode
        const shuffledWords = [...commonWords].sort(() => Math.random() - 0.5);
        while (words.length < targetWordCount) {
          const remainingNeeded = targetWordCount - words.length;
          const wordsToAdd = shuffledWords.slice(0, Math.min(remainingNeeded, shuffledWords.length));
          words.push(...wordsToAdd);
        }
      } else {
        // Repeat weak words to reach target count
        const shuffledWeakWords = [...weakWords].sort(() => Math.random() - 0.5);
        while (words.length < targetWordCount) {
          const remainingNeeded = targetWordCount - words.length;
          const wordsToAdd = shuffledWeakWords.slice(0, Math.min(remainingNeeded, shuffledWeakWords.length));
          words.push(...wordsToAdd);
        }
      }
    }

    set({
      currentWords: words,
      testState: {
        ...testState,
        currentIndex: 0,
        currentInput: '',
        isTestActive: true,
        timeLeft: 60,
        mistakes: 0,
        correctChars: 0,
        totalChars: 0,
      }
    });
  },
  updateInput: (input: string) => {
    const { testState, currentWords } = get();
    const currentWord = currentWords[testState.currentIndex];

    if (!currentWord) return;

    // Start timing when user starts typing a word
    if (input.length === 1 && testState.currentInput.length === 0) {
      // First character of the word - start timing
      const updatedWords = get().words.map(word =>
        word.id === currentWord.id
          ? { ...word, lastTypedTime: Date.now() }
          : word
      );
      set(state => ({ words: updatedWords }));
    }

    // Check if word is completed (only when space is pressed)
    if (input.endsWith(' ')) {
      const wordInput = input.trim(); // Remove the space
      const isCorrect = wordInput === currentWord.text;
      const endTime = Date.now();

      // Calculate typing time for this word
      const wordStartTime = get().words.find(w => w.id === currentWord.id)?.lastTypedTime || endTime;
      const typingTime = endTime - wordStartTime;

      // Calculate WPM for this word (characters per minute / 5)
      const wordWPM = typingTime > 0 ? (currentWord.text.length / (typingTime / 60000)) / 5 : 0;

      // Update word statistics
      const updatedWords = get().words.map(word =>
        word.id === currentWord.id
          ? {
              ...word,
              attempts: word.attempts + 1,
              correctAttempts: word.correctAttempts + (isCorrect ? 1 : 0),
              totalTime: word.totalTime + typingTime,
              averageSpeed: word.attempts > 0
                ? (word.totalTime + typingTime) / (word.attempts + 1) / 1000 * 60 / 5 // Convert to WPM
                : wordWPM,
              lastTypedTime: undefined
            }
          : word
      );

      const newIndex = testState.currentIndex + 1;

      // Update test state
      set(state => ({
        words: updatedWords,
        testState: {
          ...state.testState,
          currentIndex: newIndex,
          currentInput: '',
          mistakes: state.testState.mistakes + (isCorrect ? 0 : 1),
          correctChars: state.testState.correctChars + (isCorrect ? currentWord.text.length : 0),
          totalChars: state.testState.totalChars + currentWord.text.length,
        }
      }));

      // Check if all words are completed
      if (newIndex >= currentWords.length) {
        get().endTest();
      }
    } else {
      // Update current input (don't advance word until space is pressed)
      set(state => ({
        testState: {
          ...state.testState,
          currentInput: input,
        }
      }));
    }
  },
  setTestMode: (mode) => {
    set(state => ({
      testState: { ...state.testState, testMode: mode }
    }));
  },
  updateTimer: () => {
    set(state => ({
      testState: {
        ...state.testState,
        timeLeft: Math.max(0, state.testState.timeLeft - 1),
      }
    }));
  },
  endTest: () => {
    const { testState } = get();

    // Calculate final stats
    const timeElapsed = (60 - testState.timeLeft) / 60; // Convert to minutes
    const wpm = testState.totalChars > 0 && timeElapsed > 0
      ? Math.round((testState.totalChars / 5) / timeElapsed)
      : 0;
    const accuracy = testState.totalChars > 0
      ? Math.round((testState.correctChars / testState.totalChars) * 100)
      : 100;

    // Find weak words (words with typing speed less than 50 WPM)
    const weakWords = get().words.filter(word =>
      word.attempts > 0 && word.averageSpeed < 50
    );

    // Create test result
    const testResult: TestResult = {
      date: Date.now(),
      wpm,
      accuracy,
      weakWords: weakWords.slice(0, 10), // Top 10 weak words
    };

    // Update state
    set(state => ({
      testResults: [...state.testResults, testResult],
      testState: {
        ...state.testState,
        isTestActive: false,
        isPaused: false,
      }
    }));
  },

  restartTest: () => {
    const { initTest } = get();
    set((state) => ({
      testState: {
        ...state.testState,
        currentIndex: 0,
        currentInput: '',
        isTestActive: false,
        isPaused: false,
        timeLeft: 60,
        mistakes: 0,
        correctChars: 0,
        totalChars: 0,
      },
    }));
    initTest();
  },

  pauseTest: () => {
    set((state) => ({
      testState: {
        ...state.testState,
        isPaused: true,
      },
    }));
  },

  resumeTest: () => {
    set((state) => ({
      testState: {
        ...state.testState,
        isPaused: false,
      },
    }));
  },
}));