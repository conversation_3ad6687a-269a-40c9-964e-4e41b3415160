import { create } from 'zustand';
import { commonWords } from '../data/wordList';
import { TestState, Word, TestResult } from '../types';

interface TypingStore {
  words: Word[];
  currentWords: Word[];
  testResults: TestResult[];
  testState: TestState;
  initTest: () => void;
  updateInput: (input: string) => void;
  setTestMode: (mode: 'practice' | 'weak-words') => void;
  updateTimer: () => void;
  endTest: () => void;
}

export const useTypingStore = create<TypingStore>((set, get) => ({
  words: commonWords,
  currentWords: [],
  testResults: [],
  testState: {
    currentIndex: 0,
    currentInput: '',
    isTestActive: false,
    testMode: 'practice',
    timeLeft: 60,
    mistakes: 0,
    correctChars: 0,
    totalChars: 0,
  },
  initTest: () => {
    const { testState } = get();
    const targetWordCount = 200;

    let words: Word[] = [];

    if (testState.testMode === 'practice') {
      // Practice mode: use random words from the common words list
      const shuffledWords = [...commonWords].sort(() => Math.random() - 0.5);

      // Repeat the word list if we need more than available
      while (words.length < targetWordCount) {
        const remainingNeeded = targetWordCount - words.length;
        const wordsToAdd = shuffledWords.slice(0, Math.min(remainingNeeded, shuffledWords.length));
        words.push(...wordsToAdd);
      }
    } else {
      // Weak words mode: focus on words with typing speed less than 50 WPM
      const weakWords = get().words.filter(w => w.attempts > 0 && w.averageSpeed < 50);

      if (weakWords.length === 0) {
        // No weak words yet, use practice mode
        const shuffledWords = [...commonWords].sort(() => Math.random() - 0.5);
        while (words.length < targetWordCount) {
          const remainingNeeded = targetWordCount - words.length;
          const wordsToAdd = shuffledWords.slice(0, Math.min(remainingNeeded, shuffledWords.length));
          words.push(...wordsToAdd);
        }
      } else {
        // Repeat weak words to reach target count
        const shuffledWeakWords = [...weakWords].sort(() => Math.random() - 0.5);
        while (words.length < targetWordCount) {
          const remainingNeeded = targetWordCount - words.length;
          const wordsToAdd = shuffledWeakWords.slice(0, Math.min(remainingNeeded, shuffledWeakWords.length));
          words.push(...wordsToAdd);
        }
      }
    }

    set({
      currentWords: words,
      testState: {
        ...testState,
        currentIndex: 0,
        currentInput: '',
        isTestActive: true,
        timeLeft: 60,
        mistakes: 0,
        correctChars: 0,
        totalChars: 0,
      }
    });
  },
  updateInput: (input: string) => {
    const { testState, currentWords } = get();
    const currentWord = currentWords[testState.currentIndex];

    if (!currentWord) return;

    // Start timing when user starts typing a word
    if (input.length === 1 && testState.currentInput.length === 0) {
      // First character of the word - start timing
      const updatedWords = get().words.map(word =>
        word.id === currentWord.id
          ? { ...word, lastTypedTime: Date.now() }
          : word
      );
      set(state => ({ words: updatedWords }));
    }

    // Check if word is completed (only when space is pressed)
    if (input.endsWith(' ')) {
      const wordInput = input.trim(); // Remove the space
      const isCorrect = wordInput === currentWord.text;
      const endTime = Date.now();

      // Calculate typing time for this word
      const wordStartTime = get().words.find(w => w.id === currentWord.id)?.lastTypedTime || endTime;
      const typingTime = endTime - wordStartTime;

      // Calculate WPM for this word (characters per minute / 5)
      const wordWPM = typingTime > 0 ? (currentWord.text.length / (typingTime / 60000)) / 5 : 0;

      // Update word statistics
      const updatedWords = get().words.map(word =>
        word.id === currentWord.id
          ? {
              ...word,
              attempts: word.attempts + 1,
              correctAttempts: word.correctAttempts + (isCorrect ? 1 : 0),
              totalTime: word.totalTime + typingTime,
              averageSpeed: word.attempts > 0
                ? (word.totalTime + typingTime) / (word.attempts + 1) / 1000 * 60 / 5 // Convert to WPM
                : wordWPM,
              lastTypedTime: undefined
            }
          : word
      );

      const newIndex = testState.currentIndex + 1;

      // Update test state
      set(state => ({
        words: updatedWords,
        testState: {
          ...state.testState,
          currentIndex: newIndex,
          currentInput: '',
          mistakes: state.testState.mistakes + (isCorrect ? 0 : 1),
          correctChars: state.testState.correctChars + (isCorrect ? currentWord.text.length : 0),
          totalChars: state.testState.totalChars + currentWord.text.length,
        }
      }));

      // Check if all words are completed
      if (newIndex >= currentWords.length) {
        get().endTest();
      }
    } else {
      // Update current input (don't advance word until space is pressed)
      set(state => ({
        testState: {
          ...state.testState,
          currentInput: input,
        }
      }));
    }
  },
  setTestMode: (mode) => {
    set(state => ({
      testState: { ...state.testState, testMode: mode }
    }));
  },
  updateTimer: () => {
    set(state => ({
      testState: {
        ...state.testState,
        timeLeft: Math.max(0, state.testState.timeLeft - 1),
      }
    }));
  },
  endTest: () => {
    const { testState } = get();

    // Calculate final stats
    const timeElapsed = (60 - testState.timeLeft) / 60; // Convert to minutes
    const wpm = testState.totalChars > 0 && timeElapsed > 0
      ? Math.round((testState.totalChars / 5) / timeElapsed)
      : 0;
    const accuracy = testState.totalChars > 0
      ? Math.round((testState.correctChars / testState.totalChars) * 100)
      : 100;

    // Find weak words (words with typing speed less than 50 WPM)
    const weakWords = get().words.filter(word =>
      word.attempts > 0 && word.averageSpeed < 50
    );

    // Create test result
    const testResult: TestResult = {
      date: Date.now(),
      wpm,
      accuracy,
      weakWords: weakWords.slice(0, 10), // Top 10 weak words
    };

    // Update state
    set(state => ({
      testResults: [...state.testResults, testResult],
      testState: {
        ...state.testState,
        isTestActive: false,
      }
    }));
  }
}));