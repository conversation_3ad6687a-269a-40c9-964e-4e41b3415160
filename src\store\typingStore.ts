import { create } from 'zustand';
import { commonWords } from '../data/wordList';
import { TestState, Word, TestResult } from '../types';

interface TypingStore {
  words: Word[];
  currentWords: Word[];
  testResults: TestResult[];
  testState: TestState;
  initTest: () => void;
  updateInput: (input: string) => void;
  setTestMode: (mode: 'practice' | 'weak-words') => void;
  updateTimer: () => void;
  endTest: () => void;
}

export const useTypingStore = create<TypingStore>((set, get) => ({
  words: commonWords,
  currentWords: [],
  testResults: [],
  testState: {
    currentIndex: 0,
    currentInput: '',
    isTestActive: false,
    testMode: 'practice',
    timeLeft: 60,
    mistakes: 0,
    correctChars: 0,
    totalChars: 0,
  },
  initTest: () => {
    const { testState } = get();
    const words = testState.testMode === 'practice' 
      ? [...commonWords].sort(() => Math.random() - 0.5).slice(0, 50)
      : get().words.filter(w => w.attempts > 0 && (w.correctAttempts / w.attempts) < 0.8).slice(0, 50);
    
    set({
      currentWords: words,
      testState: {
        ...testState,
        currentIndex: 0,
        currentInput: '',
        isTestActive: true,
        timeLeft: 60,
        mistakes: 0,
        correctChars: 0,
        totalChars: 0,
      }
    });
  },
  updateInput: (input: string) => {
    // Implementation here
  },
  setTestMode: (mode) => {
    set(state => ({
      testState: { ...state.testState, testMode: mode }
    }));
  },
  updateTimer: () => {
    // Implementation here
  },
  endTest: () => {
    // Implementation here
  }
}));