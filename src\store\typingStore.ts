import { create } from 'zustand';
import { commonWords } from '../data/wordList';
import { TestState, Word, TestResult } from '../types';

interface TypingStore {
  words: Word[];
  currentWords: Word[];
  testResults: TestResult[];
  testState: TestState;
  initTest: () => void;
  updateInput: (input: string) => void;
  setTestMode: (mode: 'practice' | 'weak-words' | 'story') => void;
  updateTimer: () => void;
  endTest: () => void;
  restartTest: () => void;
  pauseTest: () => void;
  resumeTest: () => void;
}

// Story templates for generating coherent stories
const storyTemplates = [
  {
    template: "The {adjective} {noun} {verb} through the {place}. {pronoun} {verb} to {verb} a {noun} that {verb} {adverb}. When {pronoun} {verb} the {noun}, {pronoun} {verb} {adverb} and {verb} {preposition} the {place}.",
    words: {
      adjective: ['good', 'new', 'first', 'last', 'long', 'great', 'little', 'own', 'other', 'old', 'right', 'big', 'high', 'different', 'small', 'large', 'next', 'early', 'young', 'important'],
      noun: ['people', 'time', 'person', 'year', 'way', 'day', 'man', 'thing', 'woman', 'life', 'child', 'world', 'school', 'state', 'family', 'student', 'group', 'country', 'problem', 'hand', 'part', 'place', 'case', 'week', 'company', 'system', 'program', 'question', 'work', 'government', 'number', 'night', 'point', 'home', 'water', 'room', 'mother', 'area', 'money', 'story', 'fact', 'month', 'lot', 'right', 'study', 'book', 'eye', 'job', 'word', 'business', 'issue', 'side', 'kind', 'head', 'house', 'service', 'friend', 'father', 'power', 'hour', 'game', 'line', 'end', 'member', 'law', 'car', 'city', 'community', 'name', 'president', 'team', 'minute', 'idea', 'kid', 'body', 'information', 'back', 'parent', 'face', 'others', 'level', 'office', 'door', 'health', 'person', 'art', 'war', 'history', 'party', 'result', 'change', 'morning', 'reason', 'research', 'girl', 'guy', 'moment', 'air', 'teacher', 'force', 'education'],
      verb: ['be', 'have', 'do', 'say', 'get', 'make', 'go', 'know', 'take', 'see', 'come', 'think', 'look', 'want', 'give', 'use', 'find', 'tell', 'ask', 'work', 'seem', 'feel', 'try', 'leave', 'call', 'keep', 'let', 'begin', 'help', 'talk', 'turn', 'start', 'show', 'hear', 'play', 'run', 'move', 'like', 'live', 'believe', 'hold', 'bring', 'happen', 'write', 'provide', 'sit', 'stand', 'lose', 'pay', 'meet', 'include', 'continue', 'set', 'learn', 'change', 'lead', 'understand', 'watch', 'follow', 'stop', 'create', 'speak', 'read', 'allow', 'add', 'spend', 'grow', 'open', 'walk', 'win', 'offer', 'remember', 'love', 'consider', 'appear', 'buy', 'wait', 'serve', 'die', 'send', 'expect', 'build', 'stay', 'fall', 'cut', 'reach', 'kill', 'remain'],
      place: ['school', 'home', 'house', 'city', 'country', 'world', 'office', 'room', 'area', 'community', 'state', 'business', 'place', 'door', 'street', 'building', 'park', 'store', 'church', 'market', 'library', 'hospital', 'restaurant', 'hotel', 'airport', 'station', 'bridge', 'road', 'path', 'garden', 'field', 'forest', 'mountain', 'river', 'lake', 'beach', 'island'],
      pronoun: ['I', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their', 'this', 'that', 'these', 'those', 'who', 'what', 'which', 'where', 'when', 'why', 'how'],
      adverb: ['well', 'back', 'just', 'now', 'only', 'also', 'still', 'never', 'here', 'more', 'where', 'much', 'why', 'how', 'when', 'really', 'again', 'right', 'even', 'way', 'down', 'today', 'however', 'together', 'around', 'often', 'probably', 'almost', 'quickly', 'slowly', 'carefully', 'easily', 'clearly', 'simply', 'finally', 'recently', 'certainly', 'particularly', 'especially', 'exactly', 'completely', 'absolutely', 'definitely', 'obviously', 'seriously', 'suddenly', 'immediately', 'directly', 'specifically', 'generally', 'basically', 'actually', 'usually', 'normally', 'typically', 'frequently', 'regularly', 'constantly', 'always', 'sometimes', 'often', 'rarely', 'hardly', 'barely', 'nearly', 'mostly', 'mainly', 'largely', 'primarily', 'essentially', 'effectively', 'successfully', 'properly', 'correctly', 'perfectly', 'beautifully', 'wonderfully', 'amazingly', 'incredibly', 'extremely', 'very', 'quite', 'rather', 'pretty', 'fairly', 'relatively', 'somewhat', 'slightly', 'highly', 'deeply', 'strongly', 'greatly', 'significantly', 'considerably', 'substantially', 'dramatically', 'remarkably', 'surprisingly', 'unfortunately', 'fortunately', 'hopefully', 'apparently', 'obviously', 'clearly', 'certainly', 'definitely', 'probably', 'possibly', 'perhaps', 'maybe'],
      preposition: ['in', 'on', 'at', 'by', 'for', 'with', 'from', 'to', 'of', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'up', 'down', 'out', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will', 'just', 'should', 'now']
    }
  }
];

const generateStory = (): Word[] => {
  const template = storyTemplates[Math.floor(Math.random() * storyTemplates.length)];
  let story = template.template;

  // Replace placeholders with random words from each category
  Object.entries(template.words).forEach(([category, words]) => {
    const regex = new RegExp(`\\{${category}\\}`, 'g');
    story = story.replace(regex, () => {
      const randomWord = words[Math.floor(Math.random() * words.length)];
      return randomWord;
    });
  });

  // Split into sentences and add proper punctuation
  const sentences = story.split('. ');
  const finalStory = sentences.map((sentence, index) => {
    // Capitalize first letter of each sentence
    const capitalizedSentence = sentence.charAt(0).toUpperCase() + sentence.slice(1);
    // Add period to end of sentence (except last one which might already have it)
    if (index === sentences.length - 1 && !capitalizedSentence.endsWith('.')) {
      return capitalizedSentence + '.';
    } else if (index < sentences.length - 1) {
      return capitalizedSentence + '.';
    }
    return capitalizedSentence;
  }).join(' ');

  // Split into words and create Word objects
  const words = finalStory.split(/\s+/).map((word, index) => ({
    id: index,
    text: word,
    attempts: 0,
    correctAttempts: 0,
    averageSpeed: 0,
    totalTime: 0,
  }));

  return words;
};

export const useTypingStore = create<TypingStore>((set, get) => ({
  words: commonWords,
  currentWords: [],
  testResults: [],
  testState: {
    currentIndex: 0,
    currentInput: '',
    isTestActive: false,
    isPaused: false,
    testMode: 'practice',
    timeLeft: 60,
    mistakes: 0,
    correctChars: 0,
    totalChars: 0,
  },
  initTest: () => {
    const { testState } = get();
    const targetWordCount = 200;

    let words: Word[] = [];

    if (testState.testMode === 'practice') {
      // Practice mode: use random words from the common words list
      const shuffledWords = [...commonWords].sort(() => Math.random() - 0.5);

      // Repeat the word list if we need more than available
      while (words.length < targetWordCount) {
        const remainingNeeded = targetWordCount - words.length;
        const wordsToAdd = shuffledWords.slice(0, Math.min(remainingNeeded, shuffledWords.length));
        words.push(...wordsToAdd);
      }
    } else if (testState.testMode === 'story') {
      // Story mode: generate a coherent story with proper punctuation and capitalization
      words = generateStory();
    } else {
      // Weak words mode: focus on words with typing speed less than 50 WPM
      const weakWords = get().words.filter(w => w.attempts > 0 && w.averageSpeed < 50);

      if (weakWords.length === 0) {
        // No weak words yet, use practice mode
        const shuffledWords = [...commonWords].sort(() => Math.random() - 0.5);
        while (words.length < targetWordCount) {
          const remainingNeeded = targetWordCount - words.length;
          const wordsToAdd = shuffledWords.slice(0, Math.min(remainingNeeded, shuffledWords.length));
          words.push(...wordsToAdd);
        }
      } else {
        // Repeat weak words to reach target count
        const shuffledWeakWords = [...weakWords].sort(() => Math.random() - 0.5);
        while (words.length < targetWordCount) {
          const remainingNeeded = targetWordCount - words.length;
          const wordsToAdd = shuffledWeakWords.slice(0, Math.min(remainingNeeded, shuffledWeakWords.length));
          words.push(...wordsToAdd);
        }
      }
    }

    set({
      currentWords: words,
      testState: {
        ...testState,
        currentIndex: 0,
        currentInput: '',
        isTestActive: true,
        timeLeft: 60,
        mistakes: 0,
        correctChars: 0,
        totalChars: 0,
      }
    });
  },
  updateInput: (input: string) => {
    const { testState, currentWords } = get();
    const currentWord = currentWords[testState.currentIndex];

    if (!currentWord) return;

    // Start timing when user starts typing a word
    if (input.length === 1 && testState.currentInput.length === 0) {
      // First character of the word - start timing
      const updatedWords = get().words.map(word =>
        word.id === currentWord.id
          ? { ...word, lastTypedTime: Date.now() }
          : word
      );
      set(state => ({ words: updatedWords }));
    }

    // Check if word is completed (only when space is pressed)
    if (input.endsWith(' ')) {
      const wordInput = input.trim(); // Remove the space
      const isCorrect = wordInput === currentWord.text;
      const endTime = Date.now();

      // Calculate typing time for this word
      const wordStartTime = get().words.find(w => w.id === currentWord.id)?.lastTypedTime || endTime;
      const typingTime = endTime - wordStartTime;

      // Calculate WPM for this word (characters per minute / 5)
      const wordWPM = typingTime > 0 ? (currentWord.text.length / (typingTime / 60000)) / 5 : 0;

      // Update word statistics
      const updatedWords = get().words.map(word =>
        word.id === currentWord.id
          ? {
              ...word,
              attempts: word.attempts + 1,
              correctAttempts: word.correctAttempts + (isCorrect ? 1 : 0),
              totalTime: word.totalTime + typingTime,
              averageSpeed: word.attempts > 0
                ? (word.totalTime + typingTime) / (word.attempts + 1) / 1000 * 60 / 5 // Convert to WPM
                : wordWPM,
              lastTypedTime: undefined
            }
          : word
      );

      const newIndex = testState.currentIndex + 1;

      // Update test state
      set(state => ({
        words: updatedWords,
        testState: {
          ...state.testState,
          currentIndex: newIndex,
          currentInput: '',
          mistakes: state.testState.mistakes + (isCorrect ? 0 : 1),
          correctChars: state.testState.correctChars + (isCorrect ? currentWord.text.length : 0),
          totalChars: state.testState.totalChars + currentWord.text.length,
        }
      }));

      // Check if all words are completed
      if (newIndex >= currentWords.length) {
        get().endTest();
      }
    } else {
      // Update current input (don't advance word until space is pressed)
      set(state => ({
        testState: {
          ...state.testState,
          currentInput: input,
        }
      }));
    }
  },
  setTestMode: (mode) => {
    set(state => ({
      testState: { ...state.testState, testMode: mode }
    }));
  },
  updateTimer: () => {
    set(state => ({
      testState: {
        ...state.testState,
        timeLeft: Math.max(0, state.testState.timeLeft - 1),
      }
    }));
  },
  endTest: () => {
    const { testState } = get();

    // Calculate final stats
    const timeElapsed = (60 - testState.timeLeft) / 60; // Convert to minutes
    const wpm = testState.totalChars > 0 && timeElapsed > 0
      ? Math.round((testState.totalChars / 5) / timeElapsed)
      : 0;
    const accuracy = testState.totalChars > 0
      ? Math.round((testState.correctChars / testState.totalChars) * 100)
      : 100;

    // Find weak words (words with typing speed less than 50 WPM)
    const weakWords = get().words.filter(word =>
      word.attempts > 0 && word.averageSpeed < 50
    );

    // Create test result
    const testResult: TestResult = {
      date: Date.now(),
      wpm,
      accuracy,
      weakWords: weakWords.slice(0, 10), // Top 10 weak words
    };

    // Update state
    set(state => ({
      testResults: [...state.testResults, testResult],
      testState: {
        ...state.testState,
        isTestActive: false,
        isPaused: false,
      }
    }));
  },

  restartTest: () => {
    const { initTest } = get();
    set((state) => ({
      testState: {
        ...state.testState,
        currentIndex: 0,
        currentInput: '',
        isTestActive: false,
        isPaused: false,
        timeLeft: 60,
        mistakes: 0,
        correctChars: 0,
        totalChars: 0,
      },
    }));
    initTest();
  },

  pauseTest: () => {
    set((state) => ({
      testState: {
        ...state.testState,
        isPaused: true,
      },
    }));
  },

  resumeTest: () => {
    set((state) => ({
      testState: {
        ...state.testState,
        isPaused: false,
      },
    }));
  },
}));