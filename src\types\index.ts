export interface Word {
  id: number;
  text: string;
  attempts: number;
  correctAttempts: number;
  averageSpeed: number;
  totalTime: number; // Total time spent typing this word (in milliseconds)
  lastTypedTime?: number; // Timestamp when word was started
}

export interface TestResult {
  date: number;
  wpm: number;
  accuracy: number;
  weakWords: Word[];
}

export interface TestState {
  currentIndex: number;
  currentInput: string;
  isTestActive: boolean;
  isPaused: boolean;
  testMode: 'practice' | 'weak-words';
  timeLeft: number;
  mistakes: number;
  correctChars: number;
  totalChars: number;
  includeCapitals: boolean;
  includePunctuation: boolean;
  includeNumbers: boolean;
}