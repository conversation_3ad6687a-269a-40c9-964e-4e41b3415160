export interface Word {
  id: number;
  text: string;
  attempts: number;
  correctAttempts: number;
  averageSpeed: number;
}

export interface TestResult {
  date: number;
  wpm: number;
  accuracy: number;
  weakWords: Word[];
}

export interface TestState {
  currentIndex: number;
  currentInput: string;
  isTestActive: boolean;
  testMode: 'practice' | 'weak-words';
  timeLeft: number;
  mistakes: number;
  correctChars: number;
  totalChars: number;
}